import React from 'react';
import { locale } from '../locales';

interface WelcomeViewProps {
  isMacOs: boolean;
}

const WelcomeView: React.FC<WelcomeViewProps> = ({ isMacOs }) => {
  return (
    <div>
      <div className="joycoder-chart-logo"></div>
      <div className="joycoder-chart-title" style={{ textAlign: 'center' }}>
        {locale.welcome.title}
      </div>
    </div>
  );
};

export default WelcomeView;
