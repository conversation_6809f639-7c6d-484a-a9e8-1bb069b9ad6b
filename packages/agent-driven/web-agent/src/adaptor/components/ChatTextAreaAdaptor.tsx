import React, { forwardRef, useMemo, useState, useCallback, useEffect } from 'react';
import { Button, Dropdown, MenuProps, Tooltip } from 'antd';
import DynamicTextArea from 'react-textarea-autosize';
import ContextMenu from '../../components/chat/ContextMenu';
import Thumbnails from '../../components/common/Thumbnails';
import { FileImageOutlined, ApiOutlined, LikeOutlined, DownOutlined, UpOutlined } from '@ant-design/icons';
import { ChatModelConfig, DEFAULT_ASSISTANT_AVATAR } from '../locales/types';
import { useEvent } from 'react-use';
import './index.css';
import './index.scss';
import 'antd/lib/tooltip/style/index.css';
import { vscode } from '../../utils/vscode';
// import { ChatMode } from '../../../../src/shared/ChatSettings';
import { ExtensionMessage, JoyCoderApiReqInfo } from '../../../../src/shared/ExtensionMessage';
import { useExtensionState } from '../../context/ExtensionStateContext';
import { SlashCommand } from '../../utils/slash-commands';
import SlashCommandMenu from '../../components/chat/SlashCommandMenu';
import { getAllModes, ModeConfig } from '../../utils/modes';

interface ChatTextAreaAdaptorProps {
  inputValue: string;
  shouldDisableImages: boolean;
  textAreaDisabled: boolean;
  contextMenuContainerRef: React.RefObject<HTMLDivElement>;
  handleMenuMouseDown: () => void;
  placeholderText: string;
  selectedImages: string[];
  setSelectedImages: React.Dispatch<React.SetStateAction<string[]>>;
  onSend: () => void;
  onSelectImages: () => void;
  onHeightChange?: (height: number) => void;
  ref?: React.Ref<HTMLTextAreaElement>;
  textAreaRef?: any;
  highlightLayerRef?: React.RefObject<HTMLDivElement>;
  contextLayerRef?: React.RefObject<HTMLDivElement>;
  queryItems: any[];
  handleMentionSelect: (type: any, value?: string) => void;
  showContextMenu: boolean;
  setShowContextMenu: (show: boolean) => void;
  searchQuery: string;
  selectedMenuIndex: number;
  setSelectedMenuIndex: (index: number) => void;
  selectedType: any;
  isTextAreaFocused: boolean;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  handleKeyUp: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  thumbnailsHeight: number;
  handleBlur: () => void;
  handlePaste: (e: React.ClipboardEvent) => void;
  updateCursorPosition: () => void;
  updateHighlights: () => void;
  textAreaBaseHeight: number | undefined;
  handleThumbnailsHeightChange: (height: number) => void;
  setIsTextAreaFocused: (e: boolean) => void;
  setTextAreaBaseHeight: (e: number | undefined) => void;
  isStreaming: boolean;
  messages?: any;
  promptList?: any;
  handleSecondaryButtonClick: (value: string, selectedImages: string[], isTerminate?: boolean) => void;
  updateStatus?: string;
  showSlashCommandsMenu?: boolean;
  slashCommandsMenuContainerRef?: React.RefObject<HTMLDivElement>;
  selectedSlashCommandsIndex: number;
  slashCommandsQuery: string;
  setSelectedSlashCommandsIndex: (index: number) => void;
  handleSlashCommandsSelect: (cmd: SlashCommand) => void;
  setShowPromptView?: () => void;
}
export const ChatTextAreaAdaptor = forwardRef<HTMLTextAreaElement, ChatTextAreaAdaptorProps>(
  (
    {
      showContextMenu,
      contextMenuContainerRef,
      handleMentionSelect,
      searchQuery,
      handleMenuMouseDown,
      selectedMenuIndex,
      setSelectedMenuIndex,
      selectedType,
      queryItems,
      updateStatus,
      isTextAreaFocused,
      highlightLayerRef,
      contextLayerRef,
      thumbnailsHeight,
      textAreaRef,
      inputValue,
      handleInputChange,
      updateHighlights,
      handleKeyDown,
      handleKeyUp,
      setIsTextAreaFocused,
      handleBlur,
      handlePaste,
      updateCursorPosition,
      textAreaBaseHeight,
      setTextAreaBaseHeight,
      onHeightChange,
      placeholderText,
      textAreaDisabled,
      selectedImages,
      setSelectedImages,
      handleThumbnailsHeightChange,
      shouldDisableImages,
      onSelectImages,
      isStreaming,
      onSend,
      handleSecondaryButtonClick,
      messages,
      slashCommandsMenuContainerRef,
      showSlashCommandsMenu,
      selectedSlashCommandsIndex = 0,
      setSelectedSlashCommandsIndex,
      slashCommandsQuery,
      handleSlashCommandsSelect,
      setShowPromptView,
      promptList,
    },
    ref,
  ) => {
    const [ChatModeIcon] = useState<Record<string, string>>({
      architect: 'guihua',
      // ask: 'comment-discussion',
      chat: 'wenda',
      code: 'bianma',
      orchestrator: 'tiaoduzhe',
      // debug: 'debug',
      promptsButtonClicked: 'xinzeng',
    });
    const { customModes, mode } = useExtensionState();
    const [modelList, setModelList] = useState<ChatModelConfig[]>([]);
    const [modeOpen, setModeOpen] = useState<boolean>(false);
    const [modelOpen, setModelOpen] = useState<boolean>(false);
    const [config, setConfig] = useState<Record<string, any>>({
      model: '',
    });
    // const [shownTooltipMode, setShownTooltipMode] = useState<ChatSettings['mode'] | null>(null);

    const modelAvatar = useMemo(() => {
      return modelList.find((item) => item.label === config.model)?.avatar || DEFAULT_ASSISTANT_AVATAR;
    }, [config.model, modelList]);
    const getPopupContainer = (id: string) => {
      return () => document.getElementById(id) || document.createElement('div');
    };
    const setChatModel = useCallback((label: any) => {
      vscode.postMessage({
        type: 'chatgpt-set-model',
        model: label || '',
      });
    }, []);
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const onModeToggle = (newMode: any) => {
      // if (textAreaDisabled) return;
      vscode.postMessage({
        type: 'togglePlanActMode',
        chatSettings: {
          mode: newMode,
        },
        chatContent: {
          message: inputValue.trim() ? inputValue : undefined,
          images: selectedImages.length > 0 ? selectedImages : undefined,
        },
      });
      // Focus the textarea after mode toggle with slight delay
      setTimeout(() => {
        textAreaRef.current?.focus();
      }, 100);
      // vscode.postMessage({ type: 'getLatestState' });
    };
    const updateCoderMode = useCallback(
      (mode: ModeConfig) => {
        if (mode.agentId === 'promptsButtonClicked') {
          setShowPromptView?.();
          setModeOpen(false);
          return;
        }
        vscode.postMessage({
          type: 'joycoder-set-mode',
          text: mode.agentId,
        });
        if (mode.agentId) {
          const modeStr = mode.agentId === 'code' ? 'act' : mode.agentId;
          onModeToggle(modeStr);
        }
        setModeOpen(false);
      },
      [onModeToggle, setShowPromptView],
    );

    const lastMessage = useMemo(() => messages.at(-1), [messages]);
    const apiRequestFailedMessage =
      lastMessage?.ask === 'api_req_failed' // if request is retried then the latest message is a api_req_retried
        ? lastMessage?.text
        : undefined;
    const [cost, apiReqStreamingFailedMessage] = useMemo(() => {
      if (lastMessage?.text != null && lastMessage?.say === 'api_req_started') {
        const info: JoyCoderApiReqInfo = JSON.parse(lastMessage?.text);
        return [info?.cost, info?.streamingFailedMessage];
      }
      return [undefined, undefined];
    }, [lastMessage?.text, lastMessage?.say]);
    const handleMessage = useCallback(
      (e: MessageEvent) => {
        const message: ExtensionMessage = e.data;
        switch (message.type) {
          case 'updateGPTConfig':
            setModelOpen(false);
            setConfig(message?.modelConfig || {});
            break;
          case 'updateGPTModel':
            const modelList = message?.modelList || [];
            setModelList(modelList);
            break;
        }
        // textAreaRef.current is not explicitly required here since react gaurantees that ref will be stable across re-renders, and we're not using its value but its reference.
      },
      [setConfig, setModelList],
    );
    useEvent('message', handleMessage);

    const ModelMemuItems = useMemo<MenuProps['items']>(() => {
      const requireModelFeatures = config.requireModelFeatures;
      const models = modelList.filter((item) => {
        if (requireModelFeatures && requireModelFeatures.length) {
          return requireModelFeatures.every((feature: any) => item.features?.includes(feature));
        }
        return true;
      });
      return models.map((item) => {
        const features = item.features ?? [];
        const ICONS = features.map((feature) => {
          const idKey = `${item.label}_${feature}`;
          if (feature === 'vision') {
            return (
              <Tooltip
                key={idKey}
                title="支持图文多模态"
                getTooltipContainer={getPopupContainer(idKey)}
                placement="topRight"
              >
                <FileImageOutlined id={idKey} className="chatconfig_menu_content_header_icons--vision" />
              </Tooltip>
            );
          }
          if (feature === 'function_call') {
            return (
              <Tooltip
                key={idKey}
                title="支持函数调用（逻辑规划）"
                getTooltipContainer={getPopupContainer(idKey)}
                placement="topRight"
              >
                <ApiOutlined id={idKey} className="chatconfig_menu_content_header_icons--function" />
              </Tooltip>
            );
          }
          if (feature === 'recommend') {
            return (
              <Tooltip
                key={idKey}
                title="JoyCoder推荐使用"
                getTooltipContainer={getPopupContainer(idKey)}
                placement="topRight"
              >
                <LikeOutlined id={idKey} className="chatconfig_menu_content_header_icons--recommend" />
              </Tooltip>
            );
          }
          return null;
        });
        if (item.maxTotalTokens > 0) {
          ICONS.push(
            <Tooltip
              key={`${item.label}_maxToken`}
              title={`最大支持上下文长度${(item.maxTotalTokens / 1000).toFixed(0)}K`}
              getTooltipContainer={getPopupContainer(`${item.label}_maxToken`)}
              placement="topRight"
            >
              <div id={`${item.label}_maxToken`} className="chatconfig_menu_content_header_icons--tokens">
                {(item.maxTotalTokens / 1000).toFixed(0)}K
              </div>
            </Tooltip>,
          );
        }
        return {
          key: item.label,
          label: (
            <div className="chatconfig_menu" onClick={() => setChatModel(item.label)}>
              <a className="chatconfig_menu_action" target="_blank" title={item.description}>
                <img style={{ width: 14, height: 14 }} src={item.avatar || DEFAULT_ASSISTANT_AVATAR} alt={item.label} />
                <div className={`chatconfig_menu_content ${config.model === item.label ? 'active' : ''}`}>
                  <div className="chatconfig_menu_content_header">
                    {item.label}
                    <div className="chatconfig_menu_content_header_icons">{ICONS}</div>
                  </div>
                  <div className="chatconfig_menu_content_tip">{item.description}</div>
                </div>
              </a>
            </div>
          ),
        };
      });
    }, [modelList, config, setChatModel]);

    useEffect(() => {
      if (!config.modelConfig?.features?.includes('vision')) {
        setSelectedImages([]);
      }
    }, [config, setSelectedImages]);

    const [highlightPlaceholderHeight, setHighlightPlaceholderHeight] = useState(0);
    const updateHighlightPlaceholderHeight = () => {
      if (contextLayerRef?.current) {
        setHighlightPlaceholderHeight(contextLayerRef.current.offsetHeight);
      }
    };
    useEffect(() => {
      const allModes = getAllModes(customModes);
      if (!allModes.find((modeItem) => modeItem.agentId === mode)) {
        updateCoderMode(allModes[0].agentId);
      }

      updateHighlightPlaceholderHeight();
      const resizeObserver = new ResizeObserver(updateHighlightPlaceholderHeight);
      if (contextLayerRef?.current) {
        resizeObserver.observe(contextLayerRef.current);
      }
      return () => {
        if (contextLayerRef?.current) {
          resizeObserver.unobserve(contextLayerRef.current);
        }
      };
    }, []);
    return (
      <div
        className="mention-context-textarea-highlight-layer"
        style={{
          padding: '10px 20px',
          opacity: 1,
          position: 'relative',
          display: 'flex',
        }}
      >
        {showSlashCommandsMenu && (
          <div ref={slashCommandsMenuContainerRef}>
            <SlashCommandMenu
              onSelect={handleSlashCommandsSelect}
              selectedIndex={selectedSlashCommandsIndex}
              setSelectedIndex={setSelectedSlashCommandsIndex}
              onMouseDown={handleMenuMouseDown}
              query={slashCommandsQuery}
              promptList={promptList}
            />
          </div>
        )}
        {showContextMenu && (
          <div ref={contextMenuContainerRef}>
            <ContextMenu
              onSelect={handleMentionSelect}
              searchQuery={searchQuery}
              onMouseDown={handleMenuMouseDown}
              selectedIndex={selectedMenuIndex}
              setSelectedIndex={setSelectedMenuIndex}
              selectedType={selectedType}
              queryItems={queryItems}
              updateStatus={updateStatus}
            />
          </div>
        )}
        {/* {!isTextAreaFocused && (
          <div
            style={{
              position: 'absolute',
              inset: '10px 17px 10px 15px',
              border: '1px solid var(--vscode-input-border)',
              borderRadius: 6,
              pointerEvents: 'none',
              zIndex: 5,
            }}
            className="mention-context-textarea-highlight-layer-line"
          />
        )} */}
        <div
          className="mention-context-textarea-highlight-layer-bg"
          style={{
            position: 'absolute',
            top: 10,
            left: 20,
            right: 22,
            bottom: 10,
            pointerEvents: 'none',
            whiteSpace: 'pre-wrap',
            wordWrap: 'break-word',
            color: 'transparent',
            overflow: 'hidden',
            border: '1px solid',
            backgroundColor: 'rgba(33,33,34,1)',
            fontFamily: 'var(--vscode-font-family)',
            fontSize: 'var(--vscode-editor-font-size)',
            lineHeight: 'var(--vscode-editor-line-height)',
            borderRadius: 10,
            // borderBottomLeftRadius: 0,
            // borderBottomRightRadius: 0,
            // borderTopLeftRadius: 0,
            // borderTopRightRadius: 0,
            borderLeft: 0,
            borderRight: 0,
            borderTop: 0,
            borderColor: '#434343',
            borderBottom: `${thumbnailsHeight + 6}px solid transparent`,
            padding: '4px 6px 3px 6px',
          }}
        >
          <div style={{ height: highlightPlaceholderHeight }} />
          <div
            ref={highlightLayerRef}
            style={{
              overflow: 'hidden',
              height: '100%',
            }}
          ></div>
        </div>
        <div
          className={
            isTextAreaFocused
              ? 'focus mention-context-textarea-highlight-layer-inner'
              : 'mention-context-textarea-highlight-layer-inner'
          }
        >
          <div className="joycoder-context-box" ref={contextLayerRef} style={{ height: 'auto' }}></div>
          {selectedImages.length > 0 && (
            <Thumbnails
              images={selectedImages}
              setImages={setSelectedImages}
              onHeightChange={handleThumbnailsHeightChange}
              style={{
                zIndex: 2,
              }}
            />
          )}
          <DynamicTextArea
            ref={(el: any) => {
              if (typeof ref === 'function') {
                ref(el);
              } else if (ref) {
                ref.current = el;
              }
              textAreaRef.current = el;
            }}
            value={inputValue}
            disabled={textAreaDisabled}
            onChange={(e: any) => {
              handleInputChange(e);
              updateHighlights();
            }}
            onKeyDown={handleKeyDown}
            onKeyUp={handleKeyUp}
            onFocus={() => setIsTextAreaFocused(true)}
            onBlur={handleBlur}
            onPaste={handlePaste}
            onSelect={updateCursorPosition}
            onMouseUp={updateCursorPosition}
            onHeightChange={(height: any) => {
              if (textAreaBaseHeight === undefined || height < textAreaBaseHeight) {
                setTextAreaBaseHeight(height);
              }
              onHeightChange?.(height);
            }}
            placeholder={textAreaDisabled || isStreaming ? '模型输出中···' : placeholderText}
            maxRows={10}
            minRows={6}
            autoFocus={true}
            style={{
              // width: 'calc(100% - 2px)',
              width: '100%',
              boxSizing: 'border-box',
              backgroundColor: 'transparent',
              color: 'var(--vscode-input-foreground)',
              borderRadius: 6,
              fontFamily: 'var(--vscode-font-family)',
              fontSize: 'var(--vscode-editor-font-size)',
              lineHeight: 'var(--vscode-editor-line-height)',
              resize: 'none',
              overflowX: 'hidden',
              overflowY: 'scroll',
              scrollbarWidth: 'none',
              borderLeft: 0,
              borderRight: 0,
              borderTop: 0,
              borderBottom: `${thumbnailsHeight + 6}px solid transparent`,
              borderColor: 'transparent',
              padding: '10px 12px 3px',
              flex: 1,
              zIndex: 1,
              position: 'relative',
              opacity: textAreaDisabled ? 0.6 : 1,
              cursor: textAreaDisabled ? 'not-allowed' : undefined,
            }}
            onScroll={() => updateHighlights()}
            className="joycoder-coder-textarea"
          />
        </div>
        <div
          style={{
            position: 'absolute',
            left: 28,
            bottom: 9.5,
            zIndex: 1000,
            paddingBottom: 10,
            paddingRight: 20,
            display: 'flex',
            justifyContent: 'flex-start',
            alignItems: 'center',
            gap: 4,
          }}
        >
          <Dropdown
            className="joycoder-coder-mode-dropdown joycoder-chatgpt-model-dropdown"
            trigger={['click']}
            placement="top"
            disabled={textAreaDisabled && !((cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage)}
            getPopupContainer={() => {
              return document.querySelector('#tip-modes') || document.createElement('div');
            }}
            onOpenChange={(open) => {
              setModeOpen(open);
            }}
            menu={{
              items: Object.entries(
                [
                  ...getAllModes(customModes),
                  {
                    agentId: 'promptsButtonClicked',
                    name: '创建智能体',
                    type: 'action',
                    category: 'custom',
                  },
                ].reduce(
                  (acc, mode) => {
                    if (!acc[mode.category]) {
                      acc[mode.category] = [];
                    }
                    acc[mode.category].push(mode);
                    return acc;
                  },
                  {} as Record<string, any[]>,
                ),
              ).map(([key, modes]) => {
                return {
                  key: key,
                  type: 'group',
                  label: key === 'system' ? '内置智能体' : '自定义智能体',
                  children: (modes as any[]).map((mode: any, i: any) => {
                    return {
                      label: (
                        <div
                          key={i}
                          className={`joycoder-coder-mode-dropdown-option ${mode.agentId === 'promptsButtonClicked' ? 'joycoder-coder-mode-dropdown-option-action' : ''}`}
                          onClick={() => updateCoderMode(mode)}
                        >
                          <span>
                            <i
                              className={`icon iconfont icon-${ChatModeIcon[mode?.agentId] || 'zidingyizhinengtitouxiang'}`}
                            />
                          </span>
                          <span className="joycoder-coder-mode-dropdown-option-title">{mode?.name}</span>
                        </div>
                      ),
                      key: i,
                    };
                  }),
                };
              }),
            }}
          >
            <Button
              disabled={
                textAreaDisabled && !((cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage)
              }
              style={{
                border: ' 1px solid var(--vscode-input-border)',
                boxShadow: '0 2px 0 rgba(0, 0, 0, 0.015)',
                transition: 'all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)',
                userSelect: 'none',
                touchAction: 'manipulation',
                borderRadius: 2,
                borderColor: 'rgba(67, 67, 67, 0)',
                borderWidth: 1,
                background: 'rgba(255,255,255,0.05)',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                cursor:
                  textAreaDisabled && !((cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage)
                    ? 'not-allowed'
                    : undefined,
              }}
              size="small"
              id="tip-modes"
              className="joycoder-coder-mode-dropdown-button"
            >
              <i className={`icon iconfont icon-${ChatModeIcon[mode] || 'zidingyizhinengtitouxiang'}`} />{' '}
              <span>{getAllModes(customModes)?.find((item) => item.agentId === mode)?.name}</span>
              {!modeOpen && <i style={{ fontSize: '12px' }} className="icon iconfont icon-xiajiantoutianchong" />}
              {!!modeOpen && <i style={{ fontSize: '12px' }} className="icon iconfont icon-shangjiantoutianchong" />}
            </Button>
          </Dropdown>
          {/* 模型切换 */}
          <Dropdown
            menu={{ items: ModelMemuItems }}
            placement="top"
            trigger={['click']}
            disabled={textAreaDisabled && !((cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage)}
            className="joycoder-chatgpt-model-dropdown joycoder-chatgpt-model-dropdown-container"
            getPopupContainer={() => {
              return document.querySelector('#tip-models') || document.createElement('div');
            }}
            onOpenChange={(open) => {
              setModelOpen(open);
            }}
          >
            <Button
              disabled={
                textAreaDisabled && !((cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage)
              }
              style={{
                height: 20,
                minWidth: 113,
                border: ' 1px solid var(--vscode-input-border)',
                boxShadow: '0 2px 0 rgba(0, 0, 0, 0.015)',
                transition: 'all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)',
                userSelect: 'none',
                touchAction: 'manipulation',
                borderRadius: 2,
                borderColor: 'rgba(67, 67, 67, 0)',
                borderWidth: 1,
                background: 'rgba(255,255,255,0.05)',
                fontSize: 9,
                cursor:
                  textAreaDisabled && !((cost == null && apiRequestFailedMessage) || apiReqStreamingFailedMessage)
                    ? 'not-allowed'
                    : undefined,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              icon={<img className="model-icon" style={{ width: 12, height: 12 }} src={modelAvatar} alt="" />}
              size="small"
              id="tip-models"
              className="joycoder-chatgpt-model-dropdown-button"
            >
              {config.model}
              {!modelOpen && (
                <i
                  style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.5)' }}
                  className="icon iconfont icon-xiajiantoutianchong"
                />
              )}
              {!!modelOpen && (
                <i
                  style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.5)' }}
                  className="icon iconfont icon-shangjiantoutianchong"
                />
              )}
            </Button>
          </Dropdown>
          {config.modelConfig?.features?.includes('vision') && (
            <div
              onClick={() => {
                if (!shouldDisableImages) {
                  onSelectImages();
                }
              }}
              className="input-icon-button"
            >
              <i
                style={{ fontSize: 12, color: 'rgba(255, 255, 255, 0.5)' }}
                className={`${shouldDisableImages ? 'disabled' : ''} icon iconfont icon-tupian`}
              />
            </div>
          )}
        </div>
        <div
          style={{
            position: 'absolute',
            right: 31,
            display: 'flex',
            alignItems: 'flex-center',
            bottom: 10, // should be 10 but doesnt look good on mac
            zIndex: 2,
          }}
          className="joycoder-chatgpt-input-icons"
        >
          <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'flex-end' }}>
            {isStreaming || textAreaDisabled ? (
              <i
                style={{ fontSize: 15, cursor: 'pointer' }}
                className="icon iconfont icon-tingzhitianchong"
                onClick={() => handleSecondaryButtonClick(inputValue, selectedImages, true)}
              />
            ) : (
              <i
                style={{ fontSize: 14, cursor: 'pointer' }}
                className={`${textAreaDisabled ? 'disabled' : ''} icon iconfont icon-fasongtianchong`}
                onClick={() => {
                  if (!textAreaDisabled) {
                    setIsTextAreaFocused(false);
                    onSend();
                  }
                }}
              />
            )}
          </div>
        </div>
      </div>
    );
  },
);

export default ChatTextAreaAdaptor;
