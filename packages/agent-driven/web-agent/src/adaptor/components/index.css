.joycoder-chatgpt-model-dropdown {
  cursor: pointer;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top{
  box-sizing: border-box;
  color: rgba(255, 255, 255, 0.85);
  font-size: 14px;
  min-width: 160px;
  z-index: 1200;
  top: -9999px;
  display: none;
  left: -9999px;
  background: rgba(34,35,37,1);
  border: 1px solid rgba(48,48,53,1);
  border-radius: 6px;
  box-shadow:  0 4px 24px 0 rgba(0,0,0,1), 0 2px 4px 0 rgba(0,0,0,1);
}
.joycoder-chatgpt-model-dropdown.ant-dropdown-open .ant-dropdown-placement-top{
  display: block;
}
.joycoder-chatgpt-model-dropdown-container .ant-dropdown-placement-top{
  min-width: 250px!important;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu{
  position: relative;
  padding: 12px 4px;
  text-align: left;
  background-color: #1f1f1f;
  background-clip: padding-box;
  border-radius: 2px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  min-width: 160px;
  overflow: hidden;
  margin-bottom: 0;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu-item-group {
  margin: 0 4px;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu-item-group:first-child {
  border-bottom: 1px solid rgba(48,48,53,1);
  padding-bottom: 4px;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu-item-group:last-child {
  padding-top: 12px;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu-item-group:last-child .ant-dropdown-menu-item:last-child{
  border-top: 1px solid rgba(48,48,53,1);
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu-item{
  clear: both;
  margin: 0;
  /* padding: 5px 12px 5px 10px; */
  color: rgba(255, 255, 255, 0.85);
  font-weight: normal;
  font-size: 14px;
  line-height: 22px;
  cursor: pointer !important;
  transition: all 0.3s;
  overflow: hidden;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu-item:hover{
  background: rgba(48,48,53,1);
  border-radius: 4px;
}

.joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_menu {
  max-width: 250px;
  font-size: 11px;
  padding: 5px 12px 5px 10px;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_menu_action {
  display: inline-flex;
  align-items: center;
  color: #D9D9D9;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_menu_content {
  display: inline-flex;
  flex-direction: column;
  margin-left: 5px;
  font-size: 11px;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_menu_content_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_menu_content_header_icons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10px;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_btn {
  margin-left: 4px;
}
.joycoder-chatgpt-model-dropdown .ant-dropdown-menu-item .chatconfig_menu_content_tip {
  max-width: 200px;
  padding-right: 12px;
  font-size: 9px;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  opacity: 0.55;
}
.joycoder-chatgpt-model-dropdown .joycoder-dark-dropdown-menu-title-content {
  flex: auto;
}
.joycoder-chatgpt-model-dropdown .model-icon{
  width: 12px;
  height: 12px;
  /* padding-bottom: 1px; */
  border-radius: 50%;
  overflow: hidden;
  vertical-align: middle;
  border-style: none;
}
.joycoder-chatgpt-model-dropdown .model-icon + span{
  font-size: 11px;
  /* padding-bottom: 1px; */
  max-width: 90%;
  margin-left: 4px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: middle;
  display: inline-block;
  color: rgba(255, 255, 255, 0.5);
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.joycoder-chatgpt-model-dropdown .chatconfig_menu_content_header_icons--recommend{
  color: #e36459 !important;
  padding: 2px;
  border-radius: 2px;
  font-size: 9px !important;
  background: rgba(227, 100, 89, 0.15);
}
.joycoder-chatgpt-model-dropdown .chatconfig_menu_content_header_icons--function {
  color: #55b467 !important;
  padding: 2px;
  border-radius: 2px;
  font-size: 9px !important;
  background: rgba(85, 180, 103, 0.15);
  margin-left: 4px;
}
.joycoder-chatgpt-model-dropdown .chatconfig_menu_content_header_icons--tokens {
  font-size: 9px !important;
  padding: 0px 2px;
  display: block;
  line-height: 12px;
  border-radius: 2px;
  background: #333;
  color: #999;
  margin-left: 4px;
}
.joycoder-chatgpt-model-dropdown .chatconfig_menu_content_header_icons--vision {
  margin-left: 4px;
  color: #369eff !important;
  padding: 2px;
  border-radius: 2px;
  font-size: 9px !important;
  background: rgba(54, 158, 255, 0.15);
}
.joycoder-chatgpt-input-icons .codicon-send.codicon[class*='codicon-'] {
  transform: rotate(-30deg);
  height: 17px;
}
.joycoder-chatgpt-input-icons .codicon-send:before {
  content: "\ec0f";
  background: linear-gradient(197.97deg, rgba(76, 213, 255, 1) 0%, rgba(54, 87, 255, 1) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.joycoder-chatgpt-input-icons .codicon-device-camera:hover {
  background-color: rgba(255,255,255,0.08);
  border-radius: 2px;
}
.joycoder-chatgpt-input-icons .codicon-device-camera:before {
  content: "";
}
.mention-context-textarea-highlight-layer-inner{
  width: 100%;
  border-radius: 10px !important;
  box-sizing: border-box;
  overflow: hidden;
  padding: 1px;
  margin: -1px;
  border: 1px solid #303035;
}
.mention-context-textarea-highlight-layer-inner.focus {
  /* border-image: linear-gradient(109.95deg, rgba(88,177,242,1) 0%,rgba(255,38,61,1) 100%) 4 4; */
  border-radius: 10px !important;
  border: 1px solid transparent;
  border-radius: 10px;
  background: linear-gradient(109.95deg, rgba(88, 177, 242, 1) 0%, rgba(255, 38, 61, 1) 100%) border-box;
  mask-composite: exclude;
}
.vscode-light .mention-context-textarea-highlight-layer-inner {
  background: #cacaca;
}
.mention-context-textarea-highlight-layer-inner:hover {
  /* border-image: linear-gradient(109.95deg, rgba(88,177,242,1) 0%,rgba(255,38,61,1) 100%) 4 4; */
  border-radius: 10px !important;
  border: 1px solid transparent;
  border-radius: 10px;
  background: linear-gradient(109.95deg, rgba(88, 177, 242, 1) 0%, rgba(255, 38, 61, 1) 100%) border-box;
  mask-composite: exclude;
}

.joycoder-chatgpt-model-dropdown-button:hover{
  background: rgba(255,255,255,0.08) !important;
}
.vscode-light .joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top{
  color: rgba(0, 0, 0, 0.85);
}
/* .vscode-light .joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu{
  background-color: rgba(31, 31, 31, 0.1);
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
} */
.vscode-light .joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu{
  background-color: #ffff;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
}
.vscode-light .joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu-item {
  color: #616161;
}
.vscode-light .joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu-item:hover{
  background-color: #0000000a;
}
.vscode-light .joycoder-coder-mode-dropdown-button {
  color: #151515 !important;
}
.joycoder-login-btn{
  color: rgba(76,213,255,1) !important;
  cursor: pointer;
}

.vscode-light .joycoder-termination {
  width: 20px;
  height: 20px;
  font-size: 15px;
  background: url(https://storage.360buyimg.com/dist-dev/joycoder/chatDialog/light-termination.svg) no-repeat bottom center;
  cursor: pointer;
}
.joycoder-termination {
  width: 20px;
  height: 20px;
  font-size: 15px;
  background: url(https://storage.360buyimg.com/dist-dev/joycoder/chatDialog/termination.svg) no-repeat bottom center;
  cursor: pointer;
  opacity: 0.8;
}


.joycoder-coder-mode-tip,
.joycoder-chatgpt-model-dropdown .ant-tooltip{
  transform: scale(0.7);
  transform-origin: inherit;
  background-color: #1f1f1f;
}
.joycoder-coder-mode-tip .ant-tooltip-inner,
.joycoder-chatgpt-model-dropdown .ant-tooltip .ant-tooltip-inner{
  min-height: auto !important;
}
.joycoder-coder-textarea::placeholder{
  font-size: 12px;
  text-align: left;
  line-height: 1.25;
}

.joycoder-context-box{
  user-select: none;
  background-color: rgba(33,33,34,1);
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  position: relative;
  z-index: 1;
  overflow: hidden;
  height: 5px;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}
.vscode-light .joycoder-context-box{
  background-color: rgba(255,255,255,1);
}
.joycoder-context-box > div{
  display: inline-flex;
  font-size: 12px;
  background: rgba(168, 168, 168 , 0.26);
  margin: 4px 4px 0 4px;
  border-radius: 2px;
  overflow: hidden;
  height: 16px;
}
.joycoder-context-box > div.active{
  background: rgba(0, 135, 255 , 0.19);
}
.joycoder-context-box > div .pulse-chip{
  display: flex;
  align-items: center;
  justify-content: flex-start;
  max-width: 100%;
}
.joycoder-context-box > div .close-pulse-chip,
.joycoder-context-box > div .pulse-chip-icon{
  padding: 0 4px;
  font-size: 12px;
  cursor: pointer;
  vertical-align: middle;
}
