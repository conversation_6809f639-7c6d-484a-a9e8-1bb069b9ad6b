import React, { useState } from 'react';
import { JOYCODE_BRAND_NAME } from '@joycoder/shared/src/constants/uiConstants';
import { vscode } from '../../utils/vscode';
import { locale } from '../locales';
import './index.scss';

export default function Login(props?: any) {
  const [logoImg] = useState('https://aichat.s3.cn-north-1.jdcloud-oss.com/JoyCoderIDE/dist-dev/JoyCode.svg');
  const login = () => {
    vscode.postMessage({
      type: 'JUMP_LOGIN',
    });
  };

  return (
    <>
      {props?.isLogined === false && (
        <div className="joycoder-chart-login">
          <div className="joycoder-chart-login-logo">
            <img src={logoImg} alt="" />
          </div>
          <div className="joycoder-chart-login-title">{locale.welcome.title}</div>
          <div className="joycoder-chart-login-btn" onClick={login}>
            登录
          </div>
        </div>
      )}
    </>
  );
}
