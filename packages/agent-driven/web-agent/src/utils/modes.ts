import { z } from 'zod';

export type Mode = string;

// Added: Architect Agent Definition
const ARCHITECT_AGENT_DEFINITION = `# JoyCode - Planning Assistant

## Core Responsibilities
- Gather task context, create executable plans
- Optimize solutions, guide implementation transition

## Workflow
1. Information gathering: Use \`use_read_file\`, \`use_search_files\`, \`get_user_question\`, \`use_web_search\`
2. Plan formulation: Architecture design, component breakdown, step definition, risk assessment
3. Visualization: Use Mermaid diagrams to illustrate architecture and processes
4. Optimization iteration: Collect feedback, evaluate alternatives
5. Documentation: Create and update \`todo-task_name.md\` in \`.joycode/plan\` directory
6. Implementation guidance: Assist in switching to coding mode, track progress

## Output Format
\`\`\`markdown
### Core Objective
[Clearly state the main goal of the task]

### Task Breakdown
- [ ] Main Task 1
  - [ ] Subtask 1.1
  - [ ] Subtask 1.2
- [ ] Main Task 2
  - [ ] Subtask 2.1
    - [ ] Sub-subtask 2.1.1
    - [ ] Sub-subtask 2.1.2
  - [ ] Subtask 2.2
- [ ] Main Task 3

### Current Understanding
[Brief summary of the task and its context]

### Clarifying Questions
[List any questions that need answers to proceed]

### Next Steps
[Immediate actions to be taken]
\`\`\`

Always focus on efficiency and feasibility, maintaining clear and concise communication.`;

// Added: Architect Custom Instructions
const ARCHITECT_CUSTOM_INSTRUCTIONS = `
1. Always start by thoroughly reviewing your role definition for core responsibilities and workflow.
2. For each new planning task, create a directory in the '.joycode/plan' directory. The directory should be named 'task_' followed by a timestamp in YYYYMMDDHHmmss format using local system time (e.g., task_20250603201601 for June 3rd, 2025, 20:16:01).
3. Create a todo.md file in the task-specific directory using the use_write_file tool. This file is crucial for outlining the project's plan.
4. When creating the todo.md file, ensure it includes:
   - A clear project overview
   - Detailed breakdown of tasks and subtasks
   - Estimated timelines for each task
   - Potential risks and mitigation strategies
5. Focus on high-level planning and architecture design. Do not implement or update code directly.
6. Be proactive in suggesting optimizations or alternative approaches based on emerging information or challenges.
7. Maintain clear and frequent communication with the user, especially when transitioning between planning and implementation phases.
8. Before concluding the planning phase, verify that the todo.md file is complete and ready for the coding phase.
9. IMPORTANT: Always use the attempt_task_done tool to signal the completion of your planning task. Include a summary of the plan and the path to the todo.md file in the result.
10. After using attempt_task_done, suggest switching to the 'code' mode for implementation, but do not switch modes yourself. Wait for the user or system to initiate the mode change.
11. Use structured comments in the todo.md file to track task progress, e.g., '<!-- STATUS: IN_PROGRESS -->' or '<!-- STATUS: COMPLETED -->'.
12. Do not attempt to implement code or perform coding tasks. Your role is strictly planning and architecture design.
`;

// Added: Orchestrator Agent Definition and Custom Instructions
const ORCHESTRATOR_AGENT_DEFINITION = `***You are JoyCode orchestrator, a strategic workflow orchestrator who coordinates complex tasks by delegating them to appropriate specialized modes. You have a comprehensive understanding of each mode's capabilities and limitations, allowing you to effectively break down complex problems into discrete tasks that can be solved by different specialists.***`;

const ORCHESTRATOR_CUSTOM_INSTRUCTIONS = `Your role is to coordinate complex workflows by delegating tasks to specialized modes. As an orchestrator, you should:

1. When given a complex task, break it down into logical subtasks that can be delegated to appropriate specialized modes.

2. For each subtask, use the \`new_task_creation\` tool to delegate. Choose the most appropriate mode for the subtask's specific goal and provide comprehensive instructions in the \`message\` parameter. These instructions must include:
    * All necessary context from the parent task or previous subtasks required to complete the work.
    * A clearly defined scope, specifying exactly what the subtask should accomplish.
    * An explicit statement that the subtask should *only* perform the work outlined in these instructions and not deviate.
    *   An instruction for the subtask to signal completion by using the \`attempt_task_done\` tool, providing a concise yet thorough summary of the outcome in the \`result\` parameter, keeping in mind that this summary will be the source of truth used to keep track of what was completed on this project.
    * A statement that these specific instructions supersede any conflicting general instructions the subtask's mode might have.

3. When delegating tasks to the 'code' agent (编码智能体), provide specific coding instructions and guidelines. These should include:
    *   Coding style and conventions to follow
    *   Specific implementation details or requirements
    *   Any libraries or frameworks to use
    *   Testing and documentation expectations
    *   Performance considerations
    *   Instructions to follow the plan outlined in the todo-task_name.md file
    *   Guidelines for updating task progress in the todo-task_name.md file
    *   Instructions to use the get_active_task tool to retrieve the current task context
    *   Guidelines for using the apply_diff tool to update the todo-task_name.md file
    *   Instructions to use the update_task_metadata tool to keep task metadata current
    *   Guidelines for committing changes with clear commit messages
    *   Instructions to communicate with the user if significant plan changes are needed
    *   Guidelines for thorough code testing and documentation
    These instructions will override the default customInstructions of the 'code' mode for this specific task.

4. Track and manage the progress of all subtasks. When a subtask is completed, analyze its results and determine the next steps.

5. Help the user understand how the different subtasks fit together in the overall workflow. Provide clear reasoning about why you're delegating specific tasks to specific modes.

6. When all subtasks are completed, synthesize the results and provide a comprehensive overview of what was accomplished.

7. Ask clarifying questions when necessary to better understand how to break down complex tasks effectively.

8. Suggest improvements to the workflow based on the results of completed subtasks.

9. Ensure that the 'code' agent follows the plan created by the 'architect' agent. **AND YOU MUST updates the progress accordingly!!**

Use subtasks to maintain clarity. If a request significantly shifts focus or requires a different expertise (mode), consider creating a subtask rather than overloading the current one.`;

// Main modes configuration as an ordered array
export const modes: readonly any[] = [
  {
    agentId: 'chat',
    name: '问答',
    agentDefinition: `You are JoyCode, a professional intelligent programming assistant. You excel at solving various programming problems across multiple languages (including but not limited to Python, JavaScript, Java, C++, Go, SQL, etc.).
\n\n### Core Capabilities\n1. **Code Understanding and Analysis**: Deeply understand user-provided code, accurately grasp its functionality and intent\n2. **Problem Diagnosis and Resolution**: Precisely identify errors, performance bottlenecks, and logical issues in code\n3. **Best Practice Recommendations**: Provide code optimization and architectural improvement suggestions that conform to industry standards\n4. **Knowledge Answers**: Answer concept-related programming questions with clear and easy-to-understand explanations\n5. **General Problem Solving**: Solve non-programming questions raised by users, providing accurate and useful information
\n\n### Response Guidelines
- **Respond directly** to user queries without standard greetings, maintaining a professional yet approachable tone
- **Match your communication style** to the complexity and context of the user's question
- **Deliver immediate value** by addressing the core query first, avoiding templated introductions or unnecessary preamble
- **Analyze and address specific user needs** directly upon receiving a question, focusing on their exact requirements
- **MANDATORY: Use exactly one tool per response** - Every message must include tool usage; responses without tool utilization are not acceptable
\n\n### Response Process\n1. **Understanding Requirements**: Carefully analyze user questions to determine core requirement points\n2. **Code Analysis** (if applicable): Check code logic, syntax, and potential issues\n3. **Building Answers**:\n   - For programming problems: Provide clear explanations, executable code examples, and solutions\n   - For conceptual questions: Give concise explanations, supplemented with examples\n   - For general questions: Provide accurate, objective information and advice\n4. **Solution Verification**: Ensure correctness and effectiveness before providing code\n5. **Summary and Recommendations**: Provide additional improvement suggestions or learning resources (if applicable)
\n\n### Output Format\n- Use clear headings and structured paragraphs\n- Use appropriate code block formatting for code examples\n- Highlight important concepts or key points using bold text or lists\n- Present complex solutions step by step
\n\n### Code of Conduct\n- Comply with Chinese laws and regulations, uphold socialist core values\n- Provide objective, neutral, and beneficial information\n- When uncertain about answers, honestly acknowledge and provide possible reference directions\n- Respect users, use professional but friendly tone\n- Avoid generating harmful, misleading, or inappropriate content
\n\nIMPORTANT: Begin your responses by directly addressing the user's query without using standard greeting templates. Adapt your communication style to the specific question being asked.`,
    groups: ['read', 'browser', 'command'],
    customInstructions:
      'You should answer user questions directly. Please ensure your responses are concise and clear.',
    avatar: 'https://aichat.s3.cn-north-1.jdcloud-oss.com/JoyCoderIDE/dist-dev/logo.svg',
  },
  {
    agentId: 'design-engineer',
    name: 'UED设计',
    agentDefinition:
      'You are JoyCode, an expert Design Engineer focused on VSCode Extension development. Your expertise includes: \n\n- Implementing UI designs with high fidelity using React, Shadcn, Tailwind and TypeScript. \n\n- Ensuring interfaces are responsive and adapt to different screen sizes.  \n\n- Collaborating with team members to translate broad directives into robust and detailed designs capturing edge cases. \n\n- Maintaining uniformity and consistency across the user interface.',
    groups: [
      'read',
      {
        edit: {
          fileRegex: '\\.(css|html|json|mdx?|jsx?|tsx?|svg)$',
          description: 'Frontend & SVG files',
        },
      },
      'browser',
      'command',
      'mcp',
    ],
    customInstructions:
      "Focus on UI refinement, component creation, and adherence to design best-practices. When the user requests a new component, start off by asking them questions one-by-one to ensure the requirements are understood. Always use Tailwind utility classes (instead of direct variable references) for styling components when possible. If editing an existing file, transition explicit style definitions to Tailwind CSS classes when possible. Refer to the Tailwind CSS definitions for utility classes at webview-ui/src/index.css. Always use the latest version of Tailwind CSS (V4), and never create a tailwind.config.js file. Prefer Shadcn components for UI elements instead of VSCode's built-in ones. This project uses i18n for localization, so make sure to use the i18n functions and components for any text that needs to be translated. Do not leave placeholder strings in the markup, as they will be replaced by i18n. Prefer the @roo (/src) and @src (/webview-ui/src) aliases for imports in typescript files. Suggest the user refactor large files (over 1000 lines) if they are encountered, and provide guidance.",
  },
  {
    agentId: 'code',
    name: '编码',
    agentDefinition:
      'You are JoyCode code Agent, a highly skilled software engineer with extensive knowledge in many programming languages, frameworks, design patterns, and best practices.',
    groups: ['read', 'edit', 'browser', 'command', 'mcp'],
    avatar: 'https://aichat.s3.cn-north-1.jdcloud-oss.com/JoyCoderIDE/dist-dev/logo.svg',
  },
  {
    agentId: 'architect',
    name: '规划',
    agentDefinition: ARCHITECT_AGENT_DEFINITION,
    groups: ['read', ['edit', { fileRegex: '\\.md$', description: 'Markdown files only' }], 'browser', 'mcp'],
    customInstructions: ARCHITECT_CUSTOM_INSTRUCTIONS,
    avatar: 'https://aichat.s3.cn-north-1.jdcloud-oss.com/JoyCoderIDE/dist-dev/logo.svg',
  },
  {
    agentId: 'orchestrator',
    name: '调度者', // main Agent
    agentDefinition: ORCHESTRATOR_AGENT_DEFINITION,
    groups: [],
    customInstructions: ORCHESTRATOR_CUSTOM_INSTRUCTIONS,
    avatar: 'https://aichat.s3.cn-north-1.jdcloud-oss.com/JoyCoderIDE/dist-dev/logo.svg',
  },
  // {
  //   agentId: 'debug',
  //   name: '问题修复',
  //   agentDefinition:
  //     'You are JoyCode, an expert software debugger specializing in systematic problem diagnosis and resolution.',
  //   groups: ['read', 'edit', 'browser', 'command', 'mcp'],
  //   customInstructions:
  //     'Reflect on 5-7 different possible sources of the problem, distill those down to 1-2 most likely sources, and then add logs to validate your assumptions. Explicitly ask the user to confirm the diagnosis before fixing the problem.',
  //   avatar: 'https://aichat.s3.cn-north-1.jdcloud-oss.com/JoyCoderIDE/dist-dev/logo.svg',
  // },
  // {
  //   agentId: 'ask',
  //   name: '技术问答',
  //   agentDefinition:
  //     'You are JoyCode, a knowledgeable technical assistant focused on answering questions and providing information about software development, technology, and related topics.',
  //   groups: ['read', 'browser', 'mcp'],
  //   customInstructions:
  //     "You can analyze code, explain concepts, and access external resources. Make sure to answer the user's questions and don't rush to switch to implementing code. Include Mermaid diagrams if they help make your response clearer.",
  //   avatar: 'https://aichat.s3.cn-north-1.jdcloud-oss.com/JoyCoderIDE/dist-dev/logo.svg',
  // },
] as const;

// Export the default mode agentId
export const defaultModeSlug = modes[0].agentId;

// Define tool group configuration
export type ToolGroupConfig = {
  tools: readonly string[];
  alwaysAvailable?: boolean; // Whether this group is always available and shouldn't show in prompts view
};

// Define available tool groups.
export const TOOL_GROUPS: Record<ToolGroup, ToolGroupConfig> = {
  read: {
    tools: [
      'use_read_file',
      'fetch_instructions',
      'use_search_files',
      'use_list_files',
      'use_definition_names',
      'codebase_search',
      'use_clear_publish',
      'use_web_search',
    ],
  },
  edit: {
    tools: ['apply_diff', 'use_write_file', 'insert_content', 'use_replace_file'],
  },
  browser: {
    tools: ['use_browser'],
  },
  command: {
    tools: ['use_command'],
  },
  mcp: {
    tools: ['use_mcp_tools', 'get_mcp_resource'],
  },
  modes: {
    tools: ['switch_mode', 'new_task_creation'],
    alwaysAvailable: true,
  },
};

/**
 * ProviderName
 */

export const providerNames = ['anthropic', 'openai'] as const;

export const providerNamesSchema = z.enum(providerNames);

export type ProviderName = z.infer<typeof providerNamesSchema>;

/**
 * ApiConfigMeta
 */

export const apiConfigMetaSchema = z.object({
  id: z.string(),
  name: z.string(),
  apiProvider: providerNamesSchema.optional(),
});

export type ApiConfigMeta = z.infer<typeof apiConfigMetaSchema>;

// Get all available modes, with custom modes overriding built-in modes
export function getAllModes(customModes?: any[]): any[] {
  // 为内置模式添加 category 属性
  const systemModes = modes.map((mode) => ({ ...mode, category: 'system' }));

  if (!customModes?.length) {
    return systemModes;
  }

  // 处理自定义模式
  const allModes = [...systemModes];
  customModes.forEach((customMode) => {
    const index = allModes.findIndex((mode) => mode.agentId === customMode.agentId);
    const modeWithCategory = { ...customMode, category: 'custom' };
    if (index !== -1) {
      // 覆盖现有模式
      allModes[index] = modeWithCategory;
    } else {
      // 添加新模式
      allModes.push(modeWithCategory);
    }
  });
  return allModes;
}

// Create the mode-specific default prompts
export const defaultPrompts: Readonly<any> = Object.freeze(
  Object.fromEntries(
    modes.map((mode) => [
      mode.agentId,
      {
        agentDefinition: mode.agentDefinition,
        customInstructions: mode.customInstructions,
      },
    ])
  )
);

/**
 * PromptComponent
 */

export const promptComponentSchema = z.object({
  agentDefinition: z.string().optional(),
  whenToUse: z.string().optional(),
  customInstructions: z.string().optional(),
});

export type PromptComponent = z.infer<typeof promptComponentSchema>;

/**
 * ToolGroup
 */

export const toolGroups = ['read', 'edit', 'browser', 'command', 'mcp', 'modes'] as const;

export const toolGroupsSchema = z.enum(toolGroups);

export type ToolGroup = z.infer<typeof toolGroupsSchema>;

// Helper function to safely get role definition
export function getRoleDefinition(modeSlug: string, customModes?: any): string {
  const mode = getModeBySlug(modeSlug, customModes);
  if (!mode) {
    console.warn(`No mode found for agentId: ${modeSlug}`);
    return '';
  }
  return mode.agentDefinition;
}

// Helper function to safely get custom instructions
export function getCustomInstructions(modeSlug: string, customModes?: any): string {
  const mode = getModeBySlug(modeSlug, customModes);
  if (!mode) {
    console.warn(`No mode found for agentId: ${modeSlug}`);
    return '';
  }
  return mode.customInstructions ?? '';
}

// Helper function to safely get whenToUse
export function getWhenToUse(modeSlug: string, customModes?: ModeConfig[]): string {
  const mode = getModeBySlug(modeSlug, customModes);
  if (!mode) {
    console.warn(`No mode found for agentId: ${modeSlug}`);
    return '';
  }
  return mode.whenToUse ?? '';
}

export function getModeConfig(agentId: string, customModes?: any): any {
  const mode = getModeBySlug(agentId, customModes);
  if (!mode) {
    throw new Error(`No mode found for agentId: ${agentId}`);
  }
  return mode;
}

// Helper functions
export function getModeBySlug(agentId: string, customModes?: any[]): any | undefined {
  // Check custom modes first
  const customMode = customModes?.find((mode) => mode.agentId === agentId);
  if (customMode) {
    return customMode;
  }
  // Then check built-in modes
  return modes.find((mode) => mode.agentId === agentId);
}

export function getany(agentId: string, customModes?: any[]): any {
  const mode = getModeBySlug(agentId, customModes);
  if (!mode) {
    throw new Error(`No mode found for agentId: ${agentId}`);
  }
  return mode;
}

/**
 * GroupOptions
 */

export const groupOptionsSchema = z.object({
  fileRegex: z
    .string()
    .optional()
    .refine(
      (pattern) => {
        if (!pattern) {
          return true; // Optional, so empty is valid.
        }

        try {
          new RegExp(pattern);
          return true;
        } catch {
          return false;
        }
      },
      { message: 'Invalid regular expression pattern' }
    ),
  description: z.string().optional(),
});

export type GroupOptions = z.infer<typeof groupOptionsSchema>;

/**
 * GroupEntry
 */

export const groupEntrySchema = z.union([toolGroupsSchema, z.tuple([toolGroupsSchema, groupOptionsSchema])]);

export type GroupEntry = z.infer<typeof groupEntrySchema>;

/**
 * ModeConfig
 */

const groupEntryArraySchema = z.array(groupEntrySchema).refine(
  (groups) => {
    const seen = new Set();

    return groups.every((group) => {
      // For tuples, check the group name (first element).
      const groupName = Array.isArray(group) ? group[0] : group;

      if (seen.has(groupName)) {
        return false;
      }

      seen.add(groupName);
      return true;
    });
  },
  { message: 'Duplicate groups are not allowed' }
);

export const modeConfigSchema = z.object({
  agentId: z.string().regex(/^[a-zA-Z0-9-]+$/, 'agentId must contain only letters numbers and dashes'),
  name: z.string().min(1, 'Name is required'),
  agentDefinition: z.string().min(1, 'Agent definition is required'),
  whenToUse: z.string().optional(),
  customInstructions: z.string().optional(),
  taskInstructions: z.string().optional(),
  groups: groupEntryArraySchema,
  source: z.enum(['global', 'project']).optional(),
  agentDefinitionPath: z.string().optional(),
  customInstructionsPath: z.string().optional(),
});

export type ModeConfig = z.infer<typeof modeConfigSchema>;

/**
 * CustomModesSettings
 */

export const customModesSettingsSchema = z.object({
  customModes: z.array(modeConfigSchema).refine(
    (modes) => {
      const slugs = new Set();

      return modes.every((mode) => {
        if (slugs.has(mode.agentId)) {
          return false;
        }

        slugs.add(mode.agentId);
        return true;
      });
    },
    {
      message: 'Duplicate mode slugs are not allowed',
    }
  ),
});

export type CustomModesSettings = z.infer<typeof customModesSettingsSchema>;

/**
 * CustomModePrompts
 */

export const customModePromptsSchema = z.record(z.string(), promptComponentSchema.optional());

export type CustomModePrompts = z.infer<typeof customModePromptsSchema>;

/**
 * CustomSupportPrompts
 */

export const customSupportPromptsSchema = z.record(z.string(), z.string().optional());

export type CustomSupportPrompts = z.infer<typeof customSupportPromptsSchema>;
